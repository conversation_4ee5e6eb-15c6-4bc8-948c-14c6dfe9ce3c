2025-08-05 14:03:32,063 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) | application name from screen null 
2025-08-05 14:03:32,065 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) | module name from screen BILL 
2025-08-05 14:03:32,065 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) | brnCode in DisplayControllerServlet =null 
2025-08-05 14:03:32,065 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) | module in DisplayControllerServlet BILL 
2025-08-05 14:03:32,065 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) | actions is PROCESSING 
2025-08-05 14:03:32,065 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) | Session Value is : weblogic.servlet.internal.session.MemorySessionData@35d399b6 
2025-08-05 14:03:32,065 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) | Session Value is : TTVH2 
2025-08-05 14:03:32,065 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) | ######### Workflow Migration 9.3 Session Changes Starts ###################:  
2025-08-05 14:03:32,065 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) | Session Value  appName: TRADE 
2025-08-05 14:03:32,065 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) | Session Value  appid: 1 
2025-08-05 14:03:32,065 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) | Session Value  user: TTVH2 
2025-08-05 14:03:32,065 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) | Session Value  access: null 
2025-08-05 14:03:32,065 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) | Session Value  action: null 
2025-08-05 14:03:32,065 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) | Session Value  stageID: 11 
2025-08-05 14:03:32,065 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) | Session Value  stagename: PRO 
2025-08-05 14:03:32,066 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) | Session Value  productname: UNDRLC 
2025-08-05 14:03:32,066 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) | Session Value  pworkUnitNo: 1821556 
2025-08-05 14:03:32,066 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) | Session Value  queueID: 28 
2025-08-05 14:03:32,066 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) | Session Value  queueName: Import Bills 
2025-08-05 14:03:32,066 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) | Session Value  taskID: null 
2025-08-05 14:03:32,066 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) | Session Value  itemtypeid: 3 
2025-08-05 14:03:32,066 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) | ######### Workflow Migration 9.3 Session Changes Ends #####################:  
2025-08-05 14:03:32,066 |  INFO | [TTVH2] | ***********: SecurityCheckArmour:info(357) | Entering SecurityCheck 
2025-08-05 14:03:32,151 |  INFO | [TTVH2] | ***********: SecurityCheckArmour:info(357) | appKeyTRADE 
2025-08-05 14:03:32,151 |  INFO | [TTVH2] | ***********: SecurityCheckArmour:info(357) | Leaving SecurityChecktrue 
2025-08-05 14:03:32,151 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) | Checking for user Authentication true 
2025-08-05 14:03:32,151 |  INFO | [TTVH2] | ***********: SecurityCheckArmour:info(357) | Entering getUserName 
2025-08-05 14:03:32,152 | DEBUG | [TTVH2] | ***********: SecurityCheckArmour:debug(327) | :::::::got armor ticker for user 
2025-08-05 14:03:32,181 | DEBUG | [TTVH2] | ***********: SecurityCheckArmour:debug(327) | Got User from Armor is :::::::::::::TTVH2 
2025-08-05 14:03:32,181 |  INFO | [TTVH2] | ***********: SecurityCheckArmour:info(357) | Leaving  getUserName 
2025-08-05 14:03:32,181 | FATAL | [TTVH2] | ***********: SecurityCheckArmour:fatal(463) | Entering SecurityCheckArmour.getLocale 
2025-08-05 14:03:32,182 | DEBUG | [TTVH2] | ***********: SecurityCheckArmour:debug(327) | Session created 
2025-08-05 14:03:32,216 | DEBUG | [TTVH2] | ***********: SecurityCheckArmour:debug(327) | UserObj created 
2025-08-05 14:03:32,216 | DEBUG | [TTVH2] | ***********: SecurityCheckArmour:debug(327) | getting loacale  from armor 
2025-08-05 14:03:32,216 | DEBUG | [TTVH2] | ***********: SecurityCheckArmour:debug(327) | Locale obtained from armor :en_US 
2025-08-05 14:03:32,217 | DEBUG | [TTVH2] | ***********: SecurityCheckArmour:debug(327) | Entering makeLocaleObj 
2025-08-05 14:03:32,217 | DEBUG | [TTVH2] | ***********: SecurityCheckArmour:debug(327) | Leaving makeLocaleObj 
2025-08-05 14:03:32,217 | FATAL | [TTVH2] | ***********: SecurityCheckArmour:fatal(463) | Leaving SecurityCheckArmour.getLocale  
2025-08-05 14:03:32,217 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) |  DisplayControllerServlet :: Locale required from securityCheck is en_US 
2025-08-05 14:03:32,217 | DEBUG | [TTVH2] | ***********:DisplayControllerServlet:debug(327) |      UserId TTVH2 
2025-08-05 14:03:32,217 | DEBUG | [TTVH2#TRADE] | ***********:DisplayControllerServlet:debug(327) | actions :PROCESSING 
2025-08-05 14:03:32,218 | DEBUG | [TTVH2#TRADE] | ***********:DisplayControllerServlet:debug(327) | moduleName :BILL 
2025-08-05 14:03:32,218 | DEBUG | [TTVH2#TRADE] | ***********:DisplayControllerServlet:debug(327) | before calling handleBills 
2025-08-05 14:03:32,231 | DEBUG | [TTVH2#TRADE] | ***********:BillDisplayController:debug(327) | inside BillDisplayController.handleBills method 
2025-08-05 14:03:32,231 | DEBUG | [TTVH2#TRADE] | ***********:BillDisplayController:debug(327) | <h1> bill actions </h1>PROCESSING %%%%%%%%%%%%%%%%% 
2025-08-05 14:03:32,232 | DEBUG | [TTVH2#TRADE] | ***********:BillDisplayController:debug(327) | actions are =PROCESSING 
2025-08-05 14:03:32,232 | DEBUG | [TTVH2#TRADE] | ***********:BillDisplayController:debug(327) | refNo =null 
2025-08-05 14:03:32,232 | DEBUG | [TTVH2#TRADE] | ***********:BillDisplayController:debug(327) | seqNo =null 
2025-08-05 14:03:32,232 | DEBUG | [TTVH2#TRADE] | ***********:BillDisplayController:debug(327) | actNo =null 
2025-08-05 14:03:32,232 | DEBUG | [TTVH2#TRADE] | ***********:BillDisplayController:debug(327) | opnCode =null 
2025-08-05 14:03:32,232 | DEBUG | [TTVH2#TRADE] | ***********:BillDisplayController:debug(327) | cmbPkField1 =null 
2025-08-05 14:03:32,232 | DEBUG | [TTVH2#TRADE] | ***********:BillDisplayController:debug(327) | printing actions are --PROCESSING-- 
2025-08-05 14:03:32,232 | DEBUG | [TTVH2#TRADE] | ***********:BillDisplayController:debug(327) | Constants.BILL BILL 
2025-08-05 14:03:32,232 | DEBUG | [TTVH2#TRADE] | ***********:BillDisplayController:debug(327) | @@whetherRclaim : null 
2025-08-05 14:03:32,463 | DEBUG | [TTVH2#TRADE] | ***********:DisplayControllerServlet:debug(327) | Ending doGet  
2025-08-05 14:03:33,604 | FATAL | [TTVH2#TRADE] | ***********:DateHelper:fatal(463) | EnteringDateHelper  getFormat Method: 
2025-08-05 14:03:33,605 | FATAL | [TTVH2#TRADE] | ***********:DateHelper:fatal(463) | LeavingDateHelper  getFormat Method: 
2025-08-05 14:03:33,605 | FATAL | [TTVH2#TRADE] | ***********:DateHelper:fatal(463) | EnteringDateHelper  isStdDateFormat Method: 
2025-08-05 14:03:33,605 | FATAL | [TTVH2#TRADE] | ***********:DateHelper:fatal(463) | LeavingDateHelper  isStdDateFormat Method: 
2025-08-05 14:03:33,605 | FATAL | [TTVH2#TRADE] | ***********:DateHelper:fatal(463) | EnteringDateHelper  getDateFormat Method: 
2025-08-05 14:03:33,605 | FATAL | [TTVH2#TRADE] | ***********:DateHelper:fatal(463) | EnteringDateHelper  getFormat Method: 
2025-08-05 14:03:33,605 | FATAL | [TTVH2#TRADE] | ***********:DateHelper:fatal(463) | LeavingDateHelper  getFormat Method: 
2025-08-05 14:03:33,605 | FATAL | [TTVH2#TRADE] | ***********:DateHelper:fatal(463) | LeavingDateHelper  getDateFormat Method: 
2025-08-05 14:03:33,605 | FATAL | [TTVH2#TRADE] | ***********:DateHelper:fatal(463) | EnteringDateHelper  isStdDateFormat Method: 
2025-08-05 14:03:33,606 | FATAL | [TTVH2#TRADE] | ***********:DateHelper:fatal(463) | LeavingDateHelper  isStdDateFormat Method: 
2025-08-05 14:03:33,606 |  INFO | [TTVH2#TRADE] | ***********:WorkFlowHelper:info(357) | Entering WorkFlowHelper.getStageName method 
2025-08-05 14:03:33,608 |  INFO | [TTVH2#TRADE] | ***********:WorkFlowHelper:info(357) | Stage Name From Properties File isPRO 
2025-08-05 14:03:33,608 |  INFO | [TTVH2#TRADE] | ***********:WorkFlowHelper:info(357) | Leaving WorkFlowHelper.getStageName method 
2025-08-05 14:03:33,608 |  INFO | [TTVH2#TRADE] | ***********:WorkFlowHelper:info(357) | Entering WorkFlowHelper.getTxnDetails method 
2025-08-05 14:03:33,608 | DEBUG | [TTVH2#TRADE] | ***********:WorkFlowHelper:debug(327) | Database Query :Select REF_NO, SEQ_NO, ACT_NO, BRN_CODE,OPN_CODE,STATE,PROD_CODE, REG_BY,PRO_BY,AUTH_BY from OT_WORKUNIT where WKUNIT_ID = 1821556 
2025-08-05 14:03:33,608 | DEBUG | [TTVH2#TRADE] | ***********:SQLUtils:debug(327) | inside constructor of SQLUtils 
2025-08-05 14:03:33,609 | DEBUG | [TTVH2#TRADE] | ***********:SQLUtils:debug(327) | dsname is : SITPool 
2025-08-05 14:03:33,609 | DEBUG | [TTVH2#TRADE] | ***********:SQLUtils:debug(327) | ready to reAD THE PROPERTIES FILE 
2025-08-05 14:03:33,610 | DEBUG | [TTVH2#TRADE] | ***********:SQLUtils:debug(327) | dsname is : SITPool 
2025-08-05 14:03:33,611 | DEBUG | [TTVH2#TRADE] | ***********:SQLUtils:debug(327) | dataSource is : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,611 | DEBUG | [TTVH2#TRADE] | ***********:SQLUtils:debug(327) | THE CONN IS NOT NULL 
2025-08-05 14:03:33,619 | DEBUG | [TTVH2#TRADE] | ***********:WorkFlowHelper:debug(327) | BRN_CODE110200 
2025-08-05 14:03:33,619 | DEBUG | [TTVH2#TRADE] | ***********:WorkFlowHelper:debug(327) | OPN_CODEBOOKBL 
2025-08-05 14:03:33,619 | DEBUG | [TTVH2#TRADE] | ***********:WorkFlowHelper:debug(327) | STATEPROC 
2025-08-05 14:03:33,619 | DEBUG | [TTVH2#TRADE] | ***********:WorkFlowHelper:debug(327) | REF_NOE111020028622257974 
2025-08-05 14:03:33,619 | DEBUG | [TTVH2#TRADE] | ***********:WorkFlowHelper:debug(327) | S_NO001 
2025-08-05 14:03:33,619 | DEBUG | [TTVH2#TRADE] | ***********:WorkFlowHelper:debug(327) | PROD_CODEIBULCU 
2025-08-05 14:03:33,619 | DEBUG | [TTVH2#TRADE] | ***********:WorkFlowHelper:debug(327) | PRO_BYnull 
2025-08-05 14:03:33,619 |  INFO | [TTVH2#TRADE] | ***********:WorkFlowHelper:info(357) | Leaving WorkFlowHelper.getTxnDetails method 
2025-08-05 14:03:33,620 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | inside CacheManager.getCache method. 
2025-08-05 14:03:33,620 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | strMapID is MISCELLANEOUS_CACHE_MAP 
2025-08-05 14:03:33,620 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) |  objCacheObject is null in getCache NoCachedObjectFoundException 
2025-08-05 14:03:33,620 |  INFO | [TTVH2#TRADE] | ***********: MiscellaneousCacheBuilder:info(357) |  Entering MiscellaneousCacheBuilder.build 
2025-08-05 14:03:33,621 |  INFO | [TTVH2#TRADE] | ***********: MiscellaneousCacheBuilder:info(357) | Database Query :SELECT VAR_NAME, VAR_LABEL,UI_ELEMENT_CODE,UI_VALIDATION FROM OT_MST_MISC WHERE PROD_CODE = 'IBULCU' 
2025-08-05 14:03:33,621 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,621 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:33,621 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,621 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,621 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,636 |  INFO | [TTVH2#TRADE] | ***********: MiscellaneousCacheBuilder:info(357) |  Entering MiscellaneousCacheBuilder.getMiscellaneousItem 
2025-08-05 14:03:33,636 |  INFO | [TTVH2#TRADE] | ***********: MiscellaneousCacheBuilder:info(357) |  Entering MiscellaneousCacheBuilder.getTextObject 
2025-08-05 14:03:33,637 |  INFO | [TTVH2#TRADE] | ***********: MiscellaneousCacheBuilder:info(357) | Database Query :SELECT DEF_TEXT,ELEMENT_SIZE,TEXT_MAX_LENGTH FROM OT_MISC_DEF_TEXT WHERE VAR_NAME='MISC1' and PROD_CODE = 'IBULCU' 
2025-08-05 14:03:33,637 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,637 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:33,638 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,638 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,638 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,710 |  INFO | [TTVH2#TRADE] | ***********: MiscellaneousCacheBuilder:info(357) |  Entering MiscellaneousCacheBuilder.getMiscellaneousItem 
2025-08-05 14:03:33,710 |  INFO | [TTVH2#TRADE] | ***********: MiscellaneousCacheBuilder:info(357) |  Entering MiscellaneousCacheBuilder.getTextObject 
2025-08-05 14:03:33,710 |  INFO | [TTVH2#TRADE] | ***********: MiscellaneousCacheBuilder:info(357) | Database Query :SELECT DEF_TEXT,ELEMENT_SIZE,TEXT_MAX_LENGTH FROM OT_MISC_DEF_TEXT WHERE VAR_NAME='MISC2' and PROD_CODE = 'IBULCU' 
2025-08-05 14:03:33,710 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,710 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:33,710 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,711 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,711 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,713 |  INFO | [TTVH2#TRADE] | ***********: MiscellaneousCacheBuilder:info(357) |  Entering MiscellaneousCacheBuilder.getMiscellaneousItem 
2025-08-05 14:03:33,714 |  INFO | [TTVH2#TRADE] | ***********: MiscellaneousCacheBuilder:info(357) |  Entering MiscellaneousCacheBuilder.getTextObject 
2025-08-05 14:03:33,714 |  INFO | [TTVH2#TRADE] | ***********: MiscellaneousCacheBuilder:info(357) | Database Query :SELECT DEF_TEXT,ELEMENT_SIZE,TEXT_MAX_LENGTH FROM OT_MISC_DEF_TEXT WHERE VAR_NAME='MISC3' and PROD_CODE = 'IBULCU' 
2025-08-05 14:03:33,714 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,714 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:33,714 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,714 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,714 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,717 |  INFO | [TTVH2#TRADE] | ***********: MiscellaneousCacheBuilder:info(357) |  Entering MiscellaneousCacheBuilder.getMiscellaneousItem 
2025-08-05 14:03:33,717 |  INFO | [TTVH2#TRADE] | ***********: MiscellaneousCacheBuilder:info(357) |  Entering MiscellaneousCacheBuilder.getTextObject 
2025-08-05 14:03:33,718 |  INFO | [TTVH2#TRADE] | ***********: MiscellaneousCacheBuilder:info(357) | Database Query :SELECT DEF_TEXT,ELEMENT_SIZE,TEXT_MAX_LENGTH FROM OT_MISC_DEF_TEXT WHERE VAR_NAME='MISC4' and PROD_CODE = 'IBULCU' 
2025-08-05 14:03:33,718 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,718 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:33,718 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,718 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,718 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,721 |  INFO | [TTVH2#TRADE] | ***********: MiscellaneousCacheBuilder:info(357) |  Entering MiscellaneousCacheBuilder.getMiscellaneousItem 
2025-08-05 14:03:33,722 |  INFO | [TTVH2#TRADE] | ***********: MiscellaneousCacheBuilder:info(357) |  Entering MiscellaneousCacheBuilder.getTextAreaObject 
2025-08-05 14:03:33,722 |  INFO | [TTVH2#TRADE] | ***********: MiscellaneousCacheBuilder:info(357) | Database Query :SELECT DEF_TEXT,TEXT_MAX_LENGTH,TEXTBOX_ROWS,TEXTBOX_COLUMNS FROM OT_MISC_DEF_TEXT WHERE VAR_NAME='MISC5' and PROD_CODE = 'IBULCU' 
2025-08-05 14:03:33,722 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,722 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:33,722 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,722 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,722 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,726 |  INFO | [TTVH2#TRADE] | ***********: MiscellaneousCacheBuilder:info(357) |  Entering MiscellaneousCacheBuilder.getMiscellaneousItem 
2025-08-05 14:03:33,726 |  INFO | [TTVH2#TRADE] | ***********: MiscellaneousCacheBuilder:info(357) |  Entering MiscellaneousCacheBuilder.getTextAreaObject 
2025-08-05 14:03:33,726 |  INFO | [TTVH2#TRADE] | ***********: MiscellaneousCacheBuilder:info(357) | Database Query :SELECT DEF_TEXT,TEXT_MAX_LENGTH,TEXTBOX_ROWS,TEXTBOX_COLUMNS FROM OT_MISC_DEF_TEXT WHERE VAR_NAME='MISC6' and PROD_CODE = 'IBULCU' 
2025-08-05 14:03:33,726 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,726 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:33,727 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,727 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,727 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,731 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | inside CacheManager.putCache method. 
2025-08-05 14:03:33,731 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | Putting objIdentifier.IBULCU 
2025-08-05 14:03:33,731 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | Putting mapID.com.orbitech.orbitrade.util.cache.CachedObject@7d8a8e4d 
2025-08-05 14:03:33,731 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | inside CacheManager.getCache method. 
2025-08-05 14:03:33,731 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | strMapID is MISCELLANEOUS_CACHE_MAP 
2025-08-05 14:03:33,732 |  INFO | [TTVH2#TRADE] | ***********:PartyRetriever:info(357) | Entering retrievePrimaryPartyCode 
2025-08-05 14:03:33,732 |  INFO | [TTVH2#TRADE] | ***********: BranchListAccessor:info(357) |  Inside BranchListAccessor.getProduct 
2025-08-05 14:03:33,732 |  INFO | [TTVH2#TRADE] | ***********: BranchListAccessor:info(357) |  Inside BranchListAccessor.getBranch 
2025-08-05 14:03:33,732 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | inside CacheManager.getCache method. 
2025-08-05 14:03:33,732 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | strMapID is BRANCHLISTMAP 
2025-08-05 14:03:33,733 |  INFO | [TTVH2#TRADE] | ***********:PartyRetriever:info(357) | Leaving retrievePrimaryPartyCode 
2025-08-05 14:03:33,733 | DEBUG | [TTVH2#TRADE] | ***********:PartyRetriever:debug(327) | Entering : PartyRetriever.retrievePrimaryPartyType 
2025-08-05 14:03:33,733 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,733 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:33,733 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,733 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,733 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,733 | DEBUG | [TTVH2#TRADE] | ***********:PartyRetriever:debug(327) | connection :weblogic.jdbc.wrapper.PoolConnection_oracle_jdbc_driver_T4CConnection@a43 
2025-08-05 14:03:33,733 | FATAL | [TTVH2#TRADE] | ***********:QueryBuilder:fatal(463) | Entering : QueryBuilder.getPrimaryPartyDesc 
2025-08-05 14:03:33,733 |  INFO | [TTVH2#TRADE] | ***********:QueryBuilder:info(357) | Framed Query :select party_desc from ot_party_type where party_code = 'APP' 
2025-08-05 14:03:33,733 | FATAL | [TTVH2#TRADE] | ***********:QueryBuilder:fatal(463) | Leaving : QueryBuilder.getPrimaryPartyDesc 
2025-08-05 14:03:33,734 | DEBUG | [TTVH2#TRADE] | ***********:PartyRetriever:debug(327) | Party Desc: Applicant 
2025-08-05 14:03:33,735 | DEBUG | [TTVH2#TRADE] | ***********:PartyRetriever:debug(327) | Leaving : PartyRetriever.retrievePrimaryPartyDesc 
2025-08-05 14:03:33,735 |  INFO | [TTVH2#TRADE] | ***********:PartyRetriever:info(357) | Entering retrievePrimaryPartyCode 
2025-08-05 14:03:33,735 |  INFO | [TTVH2#TRADE] | ***********: BranchListAccessor:info(357) |  Inside BranchListAccessor.getProduct 
2025-08-05 14:03:33,735 |  INFO | [TTVH2#TRADE] | ***********: BranchListAccessor:info(357) |  Inside BranchListAccessor.getBranch 
2025-08-05 14:03:33,736 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | inside CacheManager.getCache method. 
2025-08-05 14:03:33,736 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | strMapID is BRANCHLISTMAP 
2025-08-05 14:03:33,736 |  INFO | [TTVH2#TRADE] | ***********:PartyRetriever:info(357) | Leaving retrievePrimaryPartyCode 
2025-08-05 14:03:33,737 | DEBUG | [TTVH2#TRADE] | ***********: RetriveBill:debug(327) |  Inside Retrivebill.getBill 
2025-08-05 14:03:33,737 |  INFO | [TTVH2#TRADE] | ***********: BranchListAccessor:info(357) |  Inside BranchListAccessor.getHierarchyVals 
2025-08-05 14:03:33,737 | DEBUG | [TTVH2#TRADE] | ***********: BranchListAccessor:debug(327) |  brnCode   110200 
2025-08-05 14:03:33,737 |  INFO | [TTVH2#TRADE] | ***********: BranchListAccessor:info(357) |  Inside BranchListAccessor.getBranch 
2025-08-05 14:03:33,737 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | inside CacheManager.getCache method. 
2025-08-05 14:03:33,737 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | strMapID is BRANCHLISTMAP 
2025-08-05 14:03:33,738 |  INFO | [TTVH2#TRADE] | ***********: BranchListAccessor:info(357) |  Inside BranchListAccessor.getHierarchyValsCollection 
2025-08-05 14:03:33,738 |  INFO | [TTVH2#TRADE] | ***********: BranchListAccessor:info(357) |  Inside BranchListAccessor.getBranch 
2025-08-05 14:03:33,738 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | inside CacheManager.getCache method. 
2025-08-05 14:03:33,738 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | strMapID is BRANCHLISTMAP 
2025-08-05 14:03:33,738 | DEBUG | [TTVH2#TRADE] | ***********: RetriveBill:debug(327) | hierarchyValsCollection[BR0001, 110200] 
2025-08-05 14:03:33,738 |  INFO | [TTVH2#TRADE] | ***********: BranchListAccessor:info(357) |  Inside BranchListAccessor.getLangCode 
2025-08-05 14:03:33,738 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | inside CacheManager.getCache method. 
2025-08-05 14:03:33,738 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | strMapID is BRANCHLISTMAP 
2025-08-05 14:03:33,738 |  INFO | [TTVH2#TRADE] | ***********:CountryCacheAccessor:info(357) | Entering getCountryList 
2025-08-05 14:03:33,738 | DEBUG | [TTVH2#TRADE] | ***********:CountryCacheAccessor:debug(327) | Transaction is Begun in CityCacheAccessor 
2025-08-05 14:03:33,738 |  INFO | [TTVH2#TRADE] | ***********:CountryCacheAccessor:info(357) | Entering getFromCache 
2025-08-05 14:03:33,739 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | inside CacheManager.getCache method. 
2025-08-05 14:03:33,739 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | strMapID is Count_CACHE_MAP 
2025-08-05 14:03:33,739 |  INFO | [TTVH2#TRADE] | ***********:CountryCacheAccessor:info(357) | Leaving getCountryList 
2025-08-05 14:03:33,739 |  INFO | [TTVH2#TRADE] | ***********:LangCacheAccessor:info(357) | Entering getLanguageList 
2025-08-05 14:03:33,739 | DEBUG | [TTVH2#TRADE] | ***********:LangCacheAccessor:debug(327) |  Inside LangCacheAccessor.getLanguageList 
2025-08-05 14:03:33,739 | DEBUG | [TTVH2#TRADE] | ***********:LangCacheAccessor:debug(327) | Transaction is Begun 
2025-08-05 14:03:33,739 |  INFO | [TTVH2#TRADE] | ***********:LangCacheAccessor:info(357) | Entering getFromCache 
2025-08-05 14:03:33,739 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | inside CacheManager.getCache method. 
2025-08-05 14:03:33,739 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | strMapID is LANG_CACHE_MAP 
2025-08-05 14:03:33,739 |  INFO | [TTVH2#TRADE] | ***********:LangCacheAccessor:info(357) | Leaving getLanguageList 
2025-08-05 14:03:33,739 |  INFO | [TTVH2#TRADE] | ***********:PartyRetriever:info(357) | Entering retrieveDlftParties 
2025-08-05 14:03:33,739 |  INFO | [TTVH2#TRADE] | ***********: DefaultPartyAccessor:info(357) | Entering getDefaultPartiesFromCache 
2025-08-05 14:03:33,739 | DEBUG | [TTVH2#TRADE] | ***********: DefaultPartyAccessor:debug(327) |  in getDefaultPartiesFromCache  
2025-08-05 14:03:33,740 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | inside CacheManager.getCache method. 
2025-08-05 14:03:33,740 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | strMapID is DEFAULTPARTYMAP 
2025-08-05 14:03:33,740 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) |  objCacheObject is null in getCache NoCachedObjectFoundException 
2025-08-05 14:03:33,740 |  INFO | [TTVH2#TRADE] | ***********:PartyTypeBuilder:info(357) | Entering build 
2025-08-05 14:03:33,740 | DEBUG | [TTVH2#TRADE] | ***********:PartyTypeBuilder:debug(327) | Obtaining the Database connection for : SITPool 
2025-08-05 14:03:33,740 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,740 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:33,740 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,740 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,740 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,740 |  INFO | [TTVH2#TRADE] | ***********:PartyTypeBuilder:info(357) | Database Query :SELECT A.PARTY_CODE, B.PARTY_DESC, A.MANDATORY, A.TO_BE_DEFAULTED,  A.PARTY_CATEGORY_LIST FROM OT_PARTY_DECN A,  OT_PARTY_TYPE B WHERE PROD_CODE ='IBULCU' AND A.PARTY_CODE = B.PARTY_CODE ORDER BY A.MANDATORY DESC, A.TO_BE_DEFAULTED DESC, A.PARTY_CODE ASC  
2025-08-05 14:03:33,757 |  INFO | [TTVH2#TRADE] | ***********:PartyTypeBuilder:info(357) | Leaving build 
2025-08-05 14:03:33,758 |  INFO | [TTVH2#TRADE] | ***********: DefaultPartyAccessor:info(357) | Entering putDefaultPartiesInCache 
2025-08-05 14:03:33,758 | DEBUG | [TTVH2#TRADE] | ***********: DefaultPartyAccessor:debug(327) | arraylist of parties before putting into cache in arrPartyList = [  partyCode = APP  mandatory = Y  partyDesc = Applicant  toBeDefaulted = true  partyCategoryList = C,O,   partyCode = BEN  mandatory = Y  partyDesc = Beneficiary  toBeDefaulted = true  partyCategoryList = C,O,   partyCode = ISB  mandatory = Y  partyDesc = Issuing Bank  toBeDefaulted = true  partyCategoryList = B,O,   partyCode = RIB  mandatory = N  partyDesc = Reimbursing Bank  toBeDefaulted = true  partyCategoryList = B,O,   partyCode = ACB  mandatory = N  partyDesc = Account With Bank  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = AG1  mandatory = N  partyDesc = Agent 1  toBeDefaulted = false  partyCategoryList = B,C,O,   partyCode = AG2  mandatory = N  partyDesc = Agent 2  toBeDefaulted = false  partyCategoryList = B,C,O,   partyCode = AG3  mandatory = N  partyDesc = Agent 3  toBeDefaulted = false  partyCategoryList = B,C,O,   partyCode = BNB  mandatory = N  partyDesc = Beneficiary Bank  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = GN1  mandatory = N  partyDesc = Agent1 Bank  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = GN2  mandatory = N  partyDesc = Agent2 Bank  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = GN3  mandatory = N  partyDesc = Agent3 Bank  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = GNB  mandatory = N  partyDesc = Agents Bank  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = IMB  mandatory = N  partyDesc = Intermediary Bank  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = IRI  mandatory = N  partyDesc = Intermediary Reimbursement Inst  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = NGB  mandatory = N  partyDesc = Negotiating Bank  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = NSB  mandatory = N  partyDesc = Nostro Bank  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = ORI  mandatory = N  partyDesc = Ordering Institution  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = PRB  mandatory = N  partyDesc = Presenting Bank  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = RCR  mandatory = N  partyDesc = Receivers Correspondent  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = SCR  mandatory = N  partyDesc = Senders Correspondent  toBeDefaulted = false  partyCategoryList = B,O] 
2025-08-05 14:03:33,758 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | inside CacheManager.putCache method. 
2025-08-05 14:03:33,758 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | Putting objIdentifier.IBULCU 
2025-08-05 14:03:33,758 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | Putting mapID.com.orbitech.orbitrade.util.cache.CachedObject@7c2aaed 
2025-08-05 14:03:33,758 |  INFO | [TTVH2#TRADE] | ***********: DefaultPartyAccessor:info(357) | Leaving putDefaultPartiesInCache 
2025-08-05 14:03:33,758 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | inside CacheManager.getCache method. 
2025-08-05 14:03:33,758 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | strMapID is DEFAULTPARTYMAP 
2025-08-05 14:03:33,758 |  INFO | [TTVH2#TRADE] | ***********: DefaultPartyAccessor:info(357) | Leaving getDefaultPartiesFromCache 
2025-08-05 14:03:33,758 |  INFO | [TTVH2#TRADE] | ***********:PartyRetriever:info(357) | Leaving retrieveDlftParties 
2025-08-05 14:03:33,759 | DEBUG | [TTVH2#TRADE] | ***********: RetriveBill:debug(327) | Default Party List = [  partyCode = APP  mandatory = Y  partyDesc = Applicant  toBeDefaulted = true  partyCategoryList = C,O,   partyCode = BEN  mandatory = Y  partyDesc = Beneficiary  toBeDefaulted = true  partyCategoryList = C,O,   partyCode = ISB  mandatory = Y  partyDesc = Issuing Bank  toBeDefaulted = true  partyCategoryList = B,O,   partyCode = RIB  mandatory = N  partyDesc = Reimbursing Bank  toBeDefaulted = true  partyCategoryList = B,O,   partyCode = ACB  mandatory = N  partyDesc = Account With Bank  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = AG1  mandatory = N  partyDesc = Agent 1  toBeDefaulted = false  partyCategoryList = B,C,O,   partyCode = AG2  mandatory = N  partyDesc = Agent 2  toBeDefaulted = false  partyCategoryList = B,C,O,   partyCode = AG3  mandatory = N  partyDesc = Agent 3  toBeDefaulted = false  partyCategoryList = B,C,O,   partyCode = BNB  mandatory = N  partyDesc = Beneficiary Bank  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = GN1  mandatory = N  partyDesc = Agent1 Bank  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = GN2  mandatory = N  partyDesc = Agent2 Bank  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = GN3  mandatory = N  partyDesc = Agent3 Bank  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = GNB  mandatory = N  partyDesc = Agents Bank  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = IMB  mandatory = N  partyDesc = Intermediary Bank  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = IRI  mandatory = N  partyDesc = Intermediary Reimbursement Inst  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = NGB  mandatory = N  partyDesc = Negotiating Bank  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = NSB  mandatory = N  partyDesc = Nostro Bank  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = ORI  mandatory = N  partyDesc = Ordering Institution  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = PRB  mandatory = N  partyDesc = Presenting Bank  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = RCR  mandatory = N  partyDesc = Receivers Correspondent  toBeDefaulted = false  partyCategoryList = B,O,   partyCode = SCR  mandatory = N  partyDesc = Senders Correspondent  toBeDefaulted = false  partyCategoryList = B,O] 
2025-08-05 14:03:33,759 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getEJBHome(String, Class) 
2025-08-05 14:03:33,760 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getEJBHome(String, Class) 
2025-08-05 14:03:33,761 | DEBUG | [TTVH2#TRADE] | ***********:BillEventHandlerEJB:debug(327) | inside BillEventHandlerEJB.getLC method 
2025-08-05 14:03:33,761 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,761 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:33,761 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,762 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,762 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,762 |  INFO | [TTVH2#TRADE] | ***********:BillEventHandlerEJB:info(357) | BillEventHandlerEJB.getBill Connection taken weblogic.jdbc.wrapper.PoolConnection_oracle_jdbc_driver_T4CConnection@a49 
2025-08-05 14:03:33,763 | DEBUG | [TTVH2#TRADE] | ***********: BillRetrival:debug(327) |  in getBill (BillRetrival) now calling Secodary Bill 
2025-08-05 14:03:33,771 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.build 
2025-08-05 14:03:33,771 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,771 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:33,771 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,771 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,772 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,772 |  INFO | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:info(357) | Bill.build Connection taken weblogic.jdbc.wrapper.PoolConnection_oracle_jdbc_driver_T4CConnection@a4a 
2025-08-05 14:03:33,772 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.getData 
2025-08-05 14:03:33,772 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  get Data for 
 SELECT REF_NO,SEQ_NO,BILL_NO,FLDR_ID, COUNTRY_CODE ,ENTITY_CODE ,BRN_CODE, PROD_CODE, OPN_CODE,PRIMARY_PARTY_TYPE , PRIMARY_PARTY_NUMBER,PRIMARY_PARTY_NAME,INITIATOR_REFERENCE_NO,STAGE,STATUS, OPERATIVE_INDICATOR,REG_BY,AUTH_BY, TO_CHAR(REG_DATE, 'MM/DD/YYYY') REG_DATE,  TO_CHAR(AUTH_DATE, 'MM/DD/YYYY') AUTH_DATE,  PRIORITY, LC_REF_NO,DISCREPANT_STATUS_CODE,DISCREPANT_STATUS,ISSUING_BANK, LC_AVAILABLE_AMT,LC_AVAILABLE_AMT_CCY, TO_CHAR(LC_EXPIRY_DATE, 'MM/DD/YYYY') LC_EXPIRY_DATE,  TO_CHAR(LATEST_SHIPMENT_DATE, 'MM/DD/YYYY') LATEST_SHIPMENT_DATE,  BILL_FACE_AMT,BILL_AMT_CCY,FINANCE_AMT_CCY,TOTAL_AMT_REALIZED,TOTAL_AMT_REALIZED_CCY, LC_AMT_TO_BE_AVAILED,LC_AMT_TO_BE_AVAILED_CCY, TO_CHAR(START_DATE, 'MM/DD/YYYY') START_DATE,  TENOR_IN_DAYS, TO_CHAR(MATURITY_DATE, 'MM/DD/YYYY') MATURITY_DATE,  TO_CHAR(DATE_OF_ACCEPTANCE, 'MM/DD/YYYY') DATE_OF_ACCEPTANCE,  CHANGE_IN_AMOUNT,AMEND_TYPE, TO_CHAR(AMEND_EFF_DATE, 'MM/DD/YYYY') AMEND_EFF_DATE,  REL_ABS_AMT_CHANGE,POSITIVE_NEGATIVE_INDICATOR,DETRIMENTAL_AMEND_INDICATOR,BENE_APPROVAL_REQD , PROPOSED_AVAILABLE_AMT, PROPOSED_AVAILABLE_AMT_CCY,  PARTIAL_SHIPMENT, INCO_TERMS,PERIOD_OF_PRSTN,  TO_CHAR(DATE_OF_NEGOTIATION, 'MM/DD/YYYY') DATE_OF_NEGOTIATION, TO_CHAR(DT_RECEIPT_DOCS, 'MM/DD/YYYY') DT_RECEIPT_DOCS, TO_CHAR(DRAFT_START_DATE, 'MM/DD/YYYY') DRAFT_START_DATE, TO_CHAR(END_DATE, 'MM/DD/YYYY') END_DATE, TRANSIT_PERIOD,FX_CONTRACT_RATE, TO_CHAR(FX_CONTRACT_DATE, 'MM/DD/YYYY') FX_CONTRACT_DATE, FX_CONTRACT_DETAILS,CONV_MODE,TRANSPORT_DOC_NO, TO_CHAR(TRANSPORT_DOC_DATE, 'MM/DD/YYYY') TRANSPORT_DOC_DATE, CARRIER_AGENT,CARRIER_DETAILS,POINT_OF_LOADING,POINT_OF_DISCHARGE,TO_CHAR(LC_ISSUE_DATE,'MM/DD/YYYY') LC_ISSUE_DATE,LC_ADDITIONAL_AMT,LC_AVAIL_ADD_AMT,LC_PROPOSED_AVAIL_ADD_AMT,SHIP_GUAR_REF_NUMBER,SHIP_GUAR_AMT,ADDITIONAL_AMT,DRAFT_DRAWN_ON,ADDITIONAL_AMT_AVAILED,USANCE_DAYS_BASE_EVENT,TO_CHAR(EVENT_DATE,'MM/DD/YYYY') EVENT_DATE,TO_CHAR(ACCEPTANCE_DATE,'MM/DD/YYYY') ACCEPTANCE_DATE,FINANCE_AMT,FINANCE_DAYS,UNDER_SHIP_GUARANTEE,SIGHT_USANCE,FINANCE,EXIT_STATUS,BASE_AMT_FOR_CHARGING,LC_AMOUNT,DISPOSAL_DOCS,CONFIRMED_AMT,OVERRIDE,DEBIT_REMIT_IND,ACTIONS, TO_CHAR(DEBIT_REMIT_DATE, 'MM/DD/YYYY') DEBIT_REMIT_DATE, CHG_CLAIMED_BY_THEM, CHG_PAYABLE_BY_THEM, CHG_CLAIMED_BY_US, CHG_PAYABLE_BY_US, CHG_CLAIMED_BY_US_AMT, CHG_PAYABLE_BY_US_AMT,TOTAL_AMT_CLAIMED,GOODS_DETAILS,  BSCC_CODE,BSCC_DESC,COST_CODE,COST_DESC,ACCOUNT_CODE,ACCOUNT_DESC,LC_CASH_MARGIN_PCT , SHIP_GUAR_TRANSPORT_DOC_NO,TO_CHAR(SHIP_GUAR_TRANSPORT_DOC_DATE, 'MM/DD/YYYY') SHIP_GUAR_TRANSPORT_DOC_DATE,SHIP_GUAR_CASH_MARGIN_PCT, TO_CHAR(SHIP_GUAR_ISSUE_DATE, 'MM/DD/YYYY') SHIP_GUAR_ISSUE_DATE, TO_CHAR(SHIP_GUAR_EXPIRY_DATE, 'MM/DD/YYYY')  SHIP_GUAR_EXPIRY_DATE,EXTERNAL_REF_NO,SOURCE,LENDING_NET_INT_RATE, OS_LOAN_PROJECT_ID, OS_LOAN_CONTRACT_ID,PROJECT_ID,CONTRACT_ID, OS_LOAN_CCY,OS_LOAN_ORIG_PRIN_AMT,OS_LOAN_OS_PRIN_AMT,OS_LOAN_OS_INT_AMT, OS_LOAN_LATE_CHARGE,TO_CHAR(OS_LOAN_START_DATE, 'MM/DD/YYYY') OS_LOAN_START_DATE,TO_CHAR(OS_LOAN_MATURITY_DATE, 'MM/DD/YYYY') OS_LOAN_MATURITY_DATE,OS_LOAN_TRADE_REF_NO,COLL_TYPE, TO_CHAR(PRO_DATE, 'MM/DD/YYYY') PRO_DATE,FINANCE_REF_NO, COMMODITY_CODE FROM OT_BILL_TXN_DETAILS  WHERE REF_NO = 'E111020028622257974' AND SEQ_NO = '001'
 
2025-08-05 14:03:33,772 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Statement obtained 
2025-08-05 14:03:33,844 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.getColumns 
2025-08-05 14:03:33,845 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | Leaving getData 
2025-08-05 14:03:33,845 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | Finished getting the data 
2025-08-05 14:03:33,845 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.getDocumentList 
2025-08-05 14:03:33,845 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  get Data for 
 SELECT REALIZATION_AMT, REALIZATION_AMT_CCY, TO_CHAR(REALIZATION_DATE, 'MM/DD/YYYY') REALIZATION_DATE,TOTAL_AMT_REALIZED_UPTO_NOW,REALIZATION_NO  FROM OT_BILL_TXN_REALIZATION  WHERE REF_NO = 'E111020028622257974' AND SEQ_NO = '001'
 
2025-08-05 14:03:33,863 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.getColumns 
2025-08-05 14:03:33,863 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | No of Documents SELECT REALIZATION_AMT, REALIZATION_AMT_CCY, TO_CHAR(REALIZATION_DATE, 'MM/DD/YYYY') REALIZATION_DATE,TOTAL_AMT_REALIZED_UPTO_NOW,REALIZATION_NO  FROM OT_BILL_TXN_REALIZATION  WHERE REF_NO = 'E111020028622257974' AND SEQ_NO = '001' 
2025-08-05 14:03:33,863 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Leaving SecondaryBillObjectBuilder.getDocumentList 
2025-08-05 14:03:33,864 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.getDocumentList 
2025-08-05 14:03:33,864 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  get Data for 
 SELECT DOCUMENT_CODE ,DOCUMENT_TYPE,DOCUMENT_DETAILS,IS_DISCREPANT,DISCREPANCY_DETAILS,NUMBER_OF_ORIGINALS,NUMBER_OF_DUPLICATES,EXAMINATION_DETAILS,EXAMINED FROM OT_BILL_TXN_DOCUMENT  WHERE REF_NO='E111020028622257974' AND SEQ_NO='001'
 
2025-08-05 14:03:33,880 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.getColumns 
2025-08-05 14:03:33,880 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | No of Documents SELECT DOCUMENT_CODE ,DOCUMENT_TYPE,DOCUMENT_DETAILS,IS_DISCREPANT,DISCREPANCY_DETAILS,NUMBER_OF_ORIGINALS,NUMBER_OF_DUPLICATES,EXAMINATION_DETAILS,EXAMINED FROM OT_BILL_TXN_DOCUMENT  WHERE REF_NO='E111020028622257974' AND SEQ_NO='001' 
2025-08-05 14:03:33,881 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Leaving SecondaryBillObjectBuilder.getDocumentList 
2025-08-05 14:03:33,881 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.getData 
2025-08-05 14:03:33,881 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  get Data for 
 SELECT MISC1,MISC2,MISC3,MISC4,MISC5,MISC6,MISC7,MISC8,  MISC9,MISC10,MISC11,MISC12  FROM OT_MISC  WHERE REF_NO='E111020028622257974' AND SEQ_NO='001'
 
2025-08-05 14:03:33,881 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Statement obtained 
2025-08-05 14:03:33,886 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.getColumns 
2025-08-05 14:03:33,886 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | Leaving getData 
2025-08-05 14:03:33,887 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  after call to misc 3 
2025-08-05 14:03:33,887 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.createBill 
2025-08-05 14:03:33,888 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | branch code from hashmap is null 
2025-08-05 14:03:33,888 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | prod code is hashmap is null 
2025-08-05 14:03:33,889 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setInt 
2025-08-05 14:03:33,889 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.createBasicInfo 
2025-08-05 14:03:33,893 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,894 |  INFO | [TTVH2#TRADE] | ***********:ClauseCacheAccessor:info(357) |  Inside ClauseCacheAccessor.getClauseDesc 
2025-08-05 14:03:33,894 |  INFO | [TTVH2#TRADE] | ***********: ClauseCacheBuilder:info(357) | Entering ClauseCacheBuilder.getDescData 
2025-08-05 14:03:33,894 |  INFO | [TTVH2#TRADE] | ***********: ClauseCacheBuilder:info(357) | Database Query : SELECT CLAUSE_MNE, CLAUSE_DESC FROM OT_CLAUSE_LIBRARY WHERE CLAUSE_CODE='CMI' 
2025-08-05 14:03:33,894 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,894 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:33,895 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,895 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,895 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,967 |  INFO | [TTVH2#TRADE] | ***********: ClauseCacheBuilder:info(357) | leaving ClauseCacheBuilder.getDescData 
2025-08-05 14:03:33,968 | DEBUG | [TTVH2#TRADE] | ***********:ClauseCacheAccessor:debug(327) | descMap::::{I20=Fabric, I1=Wheat Flour, I22=Glass, I2=Sugar, I21=Leather Raw Material, I24=Vehicle, I3=Animal and Botanical Flat, I23=Iron and Steel, I4=Gas, I26=Television, I5=Oil, I25=Electronic, I6=Cigarette, I28=Gold, I7=Machine, I27=Computer, I8=Cement, I9=Clanke, I29=DFG, I30=Others, I11=Medicine, I10=Drug, I13=Chemical Product, I12=Chemical, I15=Fertilizer, I14=Plastic,PP, I17=Paper, I16=Insectiside, I19=Motorbike, I18=Fibre} 
2025-08-05 14:03:33,968 |  INFO | [TTVH2#TRADE] | ***********:ClauseCacheAccessor:info(357) |  Leaving ClauseCacheAccessor.getClauseDesc 
2025-08-05 14:03:33,968 |  INFO | [TTVH2#TRADE] | ***********:ClauseCacheAccessor:info(357) |  Inside ClauseCacheAccessor.getClauseDesc 
2025-08-05 14:03:33,969 |  INFO | [TTVH2#TRADE] | ***********: ClauseCacheBuilder:info(357) | Entering ClauseCacheBuilder.getDescData 
2025-08-05 14:03:33,969 |  INFO | [TTVH2#TRADE] | ***********: ClauseCacheBuilder:info(357) | Database Query : SELECT CLAUSE_MNE, CLAUSE_DESC FROM OT_CLAUSE_LIBRARY WHERE CLAUSE_CODE='CME' 
2025-08-05 14:03:33,969 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,969 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:33,969 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,970 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:33,970 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:33,973 |  INFO | [TTVH2#TRADE] | ***********: ClauseCacheBuilder:info(357) | leaving ClauseCacheBuilder.getDescData 
2025-08-05 14:03:33,974 | DEBUG | [TTVH2#TRADE] | ***********:ClauseCacheAccessor:debug(327) | descMap::::{E11=Cinnamon, E10=Coal, E13=Handicraft Goods, E12=Textile, E15=Fruit And Vegetable, E14=Paper, E17=Computer, E16=Electronic Item, E1=Peanut, E18=Gold, E2=Rubber, E3=Coffee, E4=Tea, E5=Rice, E6=Cashew Nuts, E7=Pepper, E8=Sea Food, E9=Crude Oil} 
2025-08-05 14:03:33,974 |  INFO | [TTVH2#TRADE] | ***********:ClauseCacheAccessor:info(357) |  Leaving ClauseCacheAccessor.getClauseDesc 
2025-08-05 14:03:33,974 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDate 
2025-08-05 14:03:33,975 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDate 
2025-08-05 14:03:33,975 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDate 
2025-08-05 14:03:33,975 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,975 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,976 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,976 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDate 
2025-08-05 14:03:33,976 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDate 
2025-08-05 14:03:33,977 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setBool 
2025-08-05 14:03:33,977 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setBool 
2025-08-05 14:03:33,978 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,978 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,978 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,979 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDate 
2025-08-05 14:03:33,979 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDate 
2025-08-05 14:03:33,980 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setBool 
2025-08-05 14:03:33,980 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setBool 
2025-08-05 14:03:33,981 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDate 
2025-08-05 14:03:33,981 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | Actions null 
2025-08-05 14:03:33,982 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDate 
2025-08-05 14:03:33,982 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | DebitRemitDate null 
2025-08-05 14:03:33,983 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDate 
2025-08-05 14:03:33,983 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDate 
2025-08-05 14:03:33,984 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setInt 
2025-08-05 14:03:33,984 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDate 
2025-08-05 14:03:33,984 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDate 
2025-08-05 14:03:33,985 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setInt 
2025-08-05 14:03:33,985 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDate 
2025-08-05 14:03:33,985 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDate 
2025-08-05 14:03:33,985 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setInt 
2025-08-05 14:03:33,985 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDate 
2025-08-05 14:03:33,986 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDate 
2025-08-05 14:03:33,986 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDate 
2025-08-05 14:03:33,986 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setBool 
2025-08-05 14:03:33,986 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,986 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,986 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,987 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,987 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,987 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,987 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,987 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setInt 
2025-08-05 14:03:33,987 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,988 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,988 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,988 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.createChargesDetails 
2025-08-05 14:03:33,989 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  
2025-08-05 14:03:33,989 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | ChargesClaimedByThem null 
2025-08-05 14:03:33,989 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | ChargesPayableByThem null 
2025-08-05 14:03:33,990 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | ChargesClaimedByUs null 
2025-08-05 14:03:33,990 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | ChargesPayableByUs null 
2025-08-05 14:03:33,990 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,990 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,990 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | ChargesClaimedByUsAmt null 
2025-08-05 14:03:33,990 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,990 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,990 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | ChargesPayableByUsAmt null 
2025-08-05 14:03:33,990 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,990 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,991 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,991 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,991 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDate 
2025-08-05 14:03:33,991 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDate 
2025-08-05 14:03:33,991 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDouble 
2025-08-05 14:03:33,991 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDate 
2025-08-05 14:03:33,991 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | Lending Contract id null 
2025-08-05 14:03:33,991 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | Lending Project id null 
2025-08-05 14:03:33,991 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | bill basic info object is created 
2025-08-05 14:03:33,992 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDate 
2025-08-05 14:03:33,992 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.createMiscellaneous 
2025-08-05 14:03:33,993 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | misc object is created 
2025-08-05 14:03:33,993 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDate 
2025-08-05 14:03:33,993 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.setDate 
2025-08-05 14:03:33,993 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | after setting auth date and reg date 
2025-08-05 14:03:33,996 |  INFO | [TTVH2#TRADE] | ***********:BillUtil:info(357) |  Entering BillUtil.getSfIDMap method 
2025-08-05 14:03:33,996 |  INFO | [TTVH2#TRADE] | ***********:BillUtil:info(357) | Input Parameter Ref_No is null 
2025-08-05 14:03:33,996 | ERROR | [TTVH2#TRADE] | ***********:BillUtil:error(419) | Error in SfIdMap method 
java.lang.Exception
	at com.orbitech.orbitrade.bill.BillUtil.getSfIdMap(BillUtil.java:763)
	at com.orbitech.orbitrade.bill.builder.SecondaryBillObjectBuilder.build(SecondaryBillObjectBuilder.java:274)
	at com.orbitech.orbitrade.bill.BillRetrival.getBill(BillRetrival.java:63)
	at com.orbitech.orbitrade.bill.ejb.BillEventHandlerEJB.getBill(BillEventHandlerEJB.java:228)
	at com.orbitech.orbitrade.bill.ejb.BillEventHandlerEJB_8expog_EOImpl.__WL_invoke(Unknown Source)
	at weblogic.ejb.container.internal.SessionRemoteMethodInvoker.invokeInternal(SessionRemoteMethodInvoker.java:54)
	at weblogic.ejb.container.internal.SessionRemoteMethodInvoker.invoke(SessionRemoteMethodInvoker.java:21)
	at com.orbitech.orbitrade.bill.ejb.BillEventHandlerEJB_8expog_EOImpl.getBill(Unknown Source)
	at com.orbitech.orbitrade.bill.RetriveBill.getBill(RetriveBill.java:124)
	at jsp_servlet.__billmainindex_header._jspService(__billmainindex_header.java:939)
	at weblogic.servlet.jsp.JspBase.service(JspBase.java:35)
	at weblogic.servlet.internal.StubSecurityHelper$ServletServiceAction.run(StubSecurityHelper.java:286)
	at weblogic.servlet.internal.StubSecurityHelper$ServletServiceAction.run(StubSecurityHelper.java:260)
	at weblogic.servlet.internal.StubSecurityHelper.invokeServlet(StubSecurityHelper.java:137)
	at weblogic.servlet.internal.ServletStubImpl.execute(ServletStubImpl.java:350)
	at weblogic.servlet.internal.ServletStubImpl.onAddToMapException(ServletStubImpl.java:489)
	at weblogic.servlet.internal.ServletStubImpl.execute(ServletStubImpl.java:376)
	at weblogic.servlet.internal.ServletStubImpl.execute(ServletStubImpl.java:247)
	at weblogic.servlet.internal.RequestDispatcherImpl.invokeServlet(RequestDispatcherImpl.java:630)
	at weblogic.servlet.internal.RequestDispatcherImpl.include(RequestDispatcherImpl.java:502)
	at weblogic.servlet.jsp.PageContextImpl.include(PageContextImpl.java:161)
	at jsp_servlet.__billmainindex._jspService(__billmainindex.java:1788)
	at weblogic.servlet.jsp.JspBase.service(JspBase.java:35)
	at weblogic.servlet.internal.StubSecurityHelper$ServletServiceAction.run(StubSecurityHelper.java:286)
	at weblogic.servlet.internal.StubSecurityHelper$ServletServiceAction.run(StubSecurityHelper.java:260)
	at weblogic.servlet.internal.StubSecurityHelper.invokeServlet(StubSecurityHelper.java:137)
	at weblogic.servlet.internal.ServletStubImpl.execute(ServletStubImpl.java:350)
	at weblogic.servlet.internal.ServletStubImpl.onAddToMapException(ServletStubImpl.java:489)
	at weblogic.servlet.internal.ServletStubImpl.execute(ServletStubImpl.java:376)
	at weblogic.servlet.internal.TailFilter.doFilter(TailFilter.java:25)
	at weblogic.servlet.internal.FilterChainImpl.doFilter(FilterChainImpl.java:78)
	at com.intellect.filters.UserAgentCompatibleTradeFilter.doFilter(UserAgentCompatibleTradeFilter.java:20)
	at weblogic.servlet.internal.FilterChainImpl.doFilter(FilterChainImpl.java:78)
	at weblogic.servlet.internal.WebAppServletContext$ServletInvocationAction.wrapRun(WebAppServletContext.java:3701)
	at weblogic.servlet.internal.WebAppServletContext$ServletInvocationAction.run(WebAppServletContext.java:3667)
	at weblogic.security.acl.internal.AuthenticatedSubject.doAs(AuthenticatedSubject.java:326)
	at weblogic.security.service.SecurityManager.runAsForUserCode(SecurityManager.java:197)
	at weblogic.servlet.provider.WlsSecurityProvider.runAsForUserCode(WlsSecurityProvider.java:203)
	at weblogic.servlet.provider.WlsSubjectHandle.run(WlsSubjectHandle.java:71)
	at weblogic.servlet.internal.WebAppServletContext.doSecuredExecute(WebAppServletContext.java:2443)
	at weblogic.servlet.internal.WebAppServletContext.securedExecute(WebAppServletContext.java:2291)
	at weblogic.servlet.internal.WebAppServletContext.execute(WebAppServletContext.java:2269)
	at weblogic.servlet.internal.ServletRequestImpl.runInternal(ServletRequestImpl.java:1703)
	at weblogic.servlet.internal.ServletRequestImpl.run(ServletRequestImpl.java:1663)
	at weblogic.servlet.provider.ContainerSupportProviderImpl$WlsRequestExecutor.run(ContainerSupportProviderImpl.java:272)
	at weblogic.invocation.ComponentInvocationContextManager._runAs(ComponentInvocationContextManager.java:352)
	at weblogic.invocation.ComponentInvocationContextManager.runAs(ComponentInvocationContextManager.java:337)
	at weblogic.work.LivePartitionUtility.doRunWorkUnderContext(LivePartitionUtility.java:57)
	at weblogic.work.PartitionUtility.runWorkUnderContext(PartitionUtility.java:41)
	at weblogic.work.SelfTuningWorkManagerImpl.runWorkUnderContext(SelfTuningWorkManagerImpl.java:644)
	at weblogic.work.ExecuteThread.execute(ExecuteThread.java:415)
	at weblogic.work.ExecuteThread.run(ExecuteThread.java:355)
2025-08-05 14:03:33,996 |  INFO | [TTVH2#TRADE] | ***********:BillUtil:info(357) |  Leaving BillUtil.SfIdMap method 
2025-08-05 14:03:33,996 |  INFO | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:info(357) | Bill.build Connection released weblogic.jdbc.wrapper.PoolConnection_oracle_jdbc_driver_T4CConnection@a4a 
2025-08-05 14:03:33,997 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | closing the conn 
2025-08-05 14:03:33,997 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) |  Inside SecondaryBillObjectBuilder.print 
2025-08-05 14:03:33,997 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | The key RealizationDetails    value  [] 
2025-08-05 14:03:33,997 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | The key MISC_DATA    value  {MISC9=null, MISC8=null, MISC7=null, MISC6=null, MISC5=null, MISC4=null, MISC3=null, MISC2=null, MISC1=null, MISC10=null, MISC11=null, MISC12=null} 
2025-08-05 14:03:33,997 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | The key Documents    value  [] 
2025-08-05 14:03:33,998 | DEBUG | [TTVH2#TRADE] | ***********:Processing.SecondaryBillObjectBuilder:debug(327) | 


 CREATING Bill 
2025-08-05 14:03:33,998 | DEBUG | [TTVH2#TRADE] | ***********: BillRetrival:debug(327) |  fldrID **null 
2025-08-05 14:03:33,998 | DEBUG | [TTVH2#TRADE] | ***********: BillRetrival:debug(327) |  refNo **E111020028622257974 
2025-08-05 14:03:34,004 | DEBUG | [TTVH2#TRADE] | ***********: BillRetrival:debug(327) |  opnCode null 
2025-08-05 14:03:34,004 | DEBUG | [TTVH2#TRADE] | ***********: BillRetrival:debug(327) | hierarchyValsCollection:[BR0001, 110200] 
2025-08-05 14:03:34,004 | DEBUG | [TTVH2#TRADE] | ***********: BillRetrival:debug(327) | The stage isPRO 
2025-08-05 14:03:34,004 |  INFO | [TTVH2#TRADE] | ***********:ChgController:info(357) | Entering getChgData 
2025-08-05 14:03:34,004 | DEBUG | [TTVH2#TRADE] | ***********:ChgController:debug(327) | HLVAL Collection:'BR0001','110200' 
2025-08-05 14:03:34,004 |  INFO | [TTVH2#TRADE] | ***********:ChgRetrController:info(357) | Entering getChgData 
2025-08-05 14:03:34,004 |  INFO | [TTVH2#TRADE] | ***********:ChgRetr:info(357) | Entering getChgCount 
2025-08-05 14:03:34,004 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:34,005 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:34,005 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:34,005 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:34,005 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:34,005 |  INFO | [TTVH2#TRADE] | ***********:ChgRetr:info(357) | Database Query :SELECT COUNT(*) FROM OT_TXN_CHG WHERE TXN_ID='E111020028622257974001' 
2025-08-05 14:03:34,005 |  INFO | [TTVH2#TRADE] | ***********:ChgRetr:info(357) | Entering processQuery  
2025-08-05 14:03:34,009 |  INFO | [TTVH2#TRADE] | ***********:ChgRetr:info(357) | Leaving processQuery  
2025-08-05 14:03:34,010 |  INFO | [TTVH2#TRADE] | ***********:ChgRetr:info(357) | Leaving getChgCount 
2025-08-05 14:03:34,010 |  INFO | [TTVH2#TRADE] | ***********:ChgRetr:info(357) | Entering retrDefaultChgParam  
2025-08-05 14:03:34,011 |  INFO | [TTVH2#TRADE] | ***********:ChgRetr:info(357) | Database Query :SELECT CHRGDEC.CHG_TYPE,CHRGDEC.WAIVABLE,CHRGDEC.MODIFIABLE,CHRGDEC.TO_BE_DEFAULTED, CHRGDEC.HL_NO, CHRGDEC.PARTY_TO_CHARGE, CHRGDEC.COLLATERAL_FLAG FROM OT_CHG_DECN CHRGDEC WHERE CHRGDEC.PROD_TYPE ='null' AND CHRGDEC.OPN_TYPE ='null'AND CHRGDEC.HL_VAL IN ('BR0001','110200')ORDER BY CHRGDEC.HL_NO,CHRGDEC.TO_BE_DEFAULTED DESC, CHG_TYPE 
2025-08-05 14:03:34,011 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:34,011 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:34,011 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:34,011 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:34,011 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:34,012 |  INFO | [TTVH2#TRADE] | ***********:ChgRetr:info(357) | Entering processQuery  
2025-08-05 14:03:34,033 |  INFO | [TTVH2#TRADE] | ***********:ChgRetr:info(357) | Leaving processQuery  
2025-08-05 14:03:34,034 |  INFO | [TTVH2#TRADE] | ***********:ChgRetr:info(357) | Leaving retrDefaultChgParam  
2025-08-05 14:03:34,034 |  INFO | [TTVH2#TRADE] | ***********:ChgRetrController:info(357) | Leaving getChgData 
2025-08-05 14:03:34,035 |  INFO | [TTVH2#TRADE] | ***********:ChgController:info(357) | Leaving getChgData 
2025-08-05 14:03:34,035 |  INFO | [TTVH2#TRADE] | ***********:PartyManager:info(357) | Entering getParties 
2025-08-05 14:03:34,035 |  INFO | [TTVH2#TRADE] | ***********:TxnPartyControl:info(357) | Entering getParties 
2025-08-05 14:03:34,035 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:34,036 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:34,036 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:34,036 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:34,036 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:34,037 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Transaction is Begun 
2025-08-05 14:03:34,037 |  INFO | [TTVH2#TRADE] | ***********:TxnPartyControl:info(357) | Entering getAllTxnParties 
2025-08-05 14:03:34,037 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Transaction is Begun 
2025-08-05 14:03:34,038 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Obtaining the Database connection for : SITPool 
2025-08-05 14:03:34,038 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:34,038 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:34,038 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:34,038 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:34,039 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:34,039 |  INFO | [TTVH2#TRADE] | ***********:TxnPartyControl:info(357) | Database Query : SELECT PARTY_CODE, PARTY_NAME, PARTY_REF_NO,  ATTN_LINE, PARTY_ID, PARTY_CAT_CODE, PREF_LANG_CODE,  BUS_SEG_CODE, PARTY_ADDR_STREET1,PARTY_ADDR_STREET2, PARTY_ADDR_STREET3, PARTY_ADDR_STREET4, PARTY_CITY,  PARTY_STATE, PARTY_COUNTRY, PARTY_POSTAL_CODE , PREF_LANG_CODE,  PARTY_TEL_NO, PARTY_OLD_NAME, PARTY_ACCOUNT_NO,PARTY_LONG_NAME  FROM OT_TXN_PARTY  WHERE REF_NO ='E111020028622257974' AND SEQ_NO =  '001' 
2025-08-05 14:03:34,048 |  INFO | [TTVH2#TRADE] | ***********:TxnPartyControl:info(357) | after performance change 
2025-08-05 14:03:34,049 |  INFO | [TTVH2#TRADE] | ***********:TxnPartyControl:info(357) | Leaving getAllTxnParties 
2025-08-05 14:03:34,050 |  INFO | [TTVH2#TRADE] | ***********:TxnPartyControl:info(357) | Entering getAllTxnPartiesFromLatest 
2025-08-05 14:03:34,050 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Transaction is Begun 
2025-08-05 14:03:34,050 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Obtaining the Database connection for : SITPool 
2025-08-05 14:03:34,050 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:34,050 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:34,050 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:34,051 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:34,051 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:34,051 |  INFO | [TTVH2#TRADE] | ***********:TxnPartyControl:info(357) | Database Query : SELECT PARTY_CODE, PARTY_NAME, PARTY_REF_NO,  ATTN_LINE, PARTY_ID, PARTY_CAT_CODE, PREF_LANG_CODE,  BUS_SEG_CODE, PARTY_ADDR_STREET1,PARTY_ADDR_STREET2, PARTY_ADDR_STREET3, PARTY_ADDR_STREET4, PARTY_CITY,  PARTY_STATE, PARTY_COUNTRY,  PARTY_POSTAL_CODE , PREF_LANG_CODE , PARTY_TEL_NO, PARTY_OLD_NAME, PARTY_ACCOUNT_NO,PARTY_LONG_NAME   FROM OT_TXN_PARTY_LATEST  WHERE REF_NO ='E111020028622257974'  
2025-08-05 14:03:34,084 |  INFO | [TTVH2#TRADE] | ***********:TxnPartyControl:info(357) | Leaving getAllTxnPartiesFromLatest 
2025-08-05 14:03:34,085 |  INFO | [TTVH2#TRADE] | ***********:TxnPartyControl:info(357) | Entering getPrimaryPartyInfo 
2025-08-05 14:03:34,085 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) |  partyCategory = null 
2025-08-05 14:03:34,085 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) |  partyId = null 
2025-08-05 14:03:34,086 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) |  The primary Party data from orbicore =null 
2025-08-05 14:03:34,086 |  INFO | [TTVH2#TRADE] | ***********:TxnPartyControl:info(357) | Leaving getPrimaryPartyInfo 
2025-08-05 14:03:34,086 |  INFO | [TTVH2#TRADE] | ***********:TxnPartyControl:info(357) | Entering compareAndMerge 
2025-08-05 14:03:34,086 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Default Party object's party Code is = APP 
2025-08-05 14:03:34,086 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | arrFinalInner[APP, Applicant, Y, true, C,O] 
2025-08-05 14:03:34,087 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Default Party object's party Code is = BEN 
2025-08-05 14:03:34,087 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | arrFinalInner[BEN, Beneficiary, Y, true, C,O] 
2025-08-05 14:03:34,087 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Default Party object's party Code is = ISB 
2025-08-05 14:03:34,087 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | arrFinalInner[ISB, Issuing Bank, Y, true, B,O] 
2025-08-05 14:03:34,088 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Default Party object's party Code is = RIB 
2025-08-05 14:03:34,088 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | arrFinalInner[RIB, Reimbursing Bank, N, true, B,O] 
2025-08-05 14:03:34,088 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Default Party object's party Code is = ACB 
2025-08-05 14:03:34,088 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | arrFinalInner[ACB, Account With Bank, N, false, B,O] 
2025-08-05 14:03:34,089 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Default Party object's party Code is = AG1 
2025-08-05 14:03:34,089 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | arrFinalInner[AG1, Agent 1, N, false, B,C,O] 
2025-08-05 14:03:34,089 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Default Party object's party Code is = AG2 
2025-08-05 14:03:34,089 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | arrFinalInner[AG2, Agent 2, N, false, B,C,O] 
2025-08-05 14:03:34,089 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Default Party object's party Code is = AG3 
2025-08-05 14:03:34,090 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | arrFinalInner[AG3, Agent 3, N, false, B,C,O] 
2025-08-05 14:03:34,090 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Default Party object's party Code is = BNB 
2025-08-05 14:03:34,090 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | arrFinalInner[BNB, Beneficiary Bank, N, false, B,O] 
2025-08-05 14:03:34,090 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Default Party object's party Code is = GN1 
2025-08-05 14:03:34,090 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | arrFinalInner[GN1, Agent1 Bank, N, false, B,O] 
2025-08-05 14:03:34,091 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Default Party object's party Code is = GN2 
2025-08-05 14:03:34,091 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | arrFinalInner[GN2, Agent2 Bank, N, false, B,O] 
2025-08-05 14:03:34,091 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Default Party object's party Code is = GN3 
2025-08-05 14:03:34,091 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | arrFinalInner[GN3, Agent3 Bank, N, false, B,O] 
2025-08-05 14:03:34,092 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Default Party object's party Code is = GNB 
2025-08-05 14:03:34,092 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | arrFinalInner[GNB, Agents Bank, N, false, B,O] 
2025-08-05 14:03:34,092 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Default Party object's party Code is = IMB 
2025-08-05 14:03:34,092 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | arrFinalInner[IMB, Intermediary Bank, N, false, B,O] 
2025-08-05 14:03:34,092 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Default Party object's party Code is = IRI 
2025-08-05 14:03:34,093 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | arrFinalInner[IRI, Intermediary Reimbursement Inst, N, false, B,O] 
2025-08-05 14:03:34,093 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Default Party object's party Code is = NGB 
2025-08-05 14:03:34,093 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | arrFinalInner[NGB, Negotiating Bank, N, false, B,O] 
2025-08-05 14:03:34,093 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Default Party object's party Code is = NSB 
2025-08-05 14:03:34,093 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | arrFinalInner[NSB, Nostro Bank, N, false, B,O] 
2025-08-05 14:03:34,094 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Default Party object's party Code is = ORI 
2025-08-05 14:03:34,094 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | arrFinalInner[ORI, Ordering Institution, N, false, B,O] 
2025-08-05 14:03:34,094 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Default Party object's party Code is = PRB 
2025-08-05 14:03:34,094 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | arrFinalInner[PRB, Presenting Bank, N, false, B,O] 
2025-08-05 14:03:34,095 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Default Party object's party Code is = RCR 
2025-08-05 14:03:34,095 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | arrFinalInner[RCR, Receivers Correspondent, N, false, B,O] 
2025-08-05 14:03:34,095 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | Default Party object's party Code is = SCR 
2025-08-05 14:03:34,095 | DEBUG | [TTVH2#TRADE] | ***********:TxnPartyControl:debug(327) | arrFinalInner[SCR, Senders Correspondent, N, false, B,O] 
2025-08-05 14:03:34,095 |  INFO | [TTVH2#TRADE] | ***********:TxnPartyControl:info(357) | Leaving compareAndMerge 
2025-08-05 14:03:34,096 |  INFO | [TTVH2#TRADE] | ***********:TxnPartyControl:info(357) | Leaving getParties 
2025-08-05 14:03:34,096 |  INFO | [TTVH2#TRADE] | ***********:PartyManager:info(357) | Leaving getParties 
2025-08-05 14:03:34,096 |  INFO | [TTVH2#TRADE] | ***********:CorresTypeManager:info(357) | Entering CorresTypeManager.getCorresListForLC()  
2025-08-05 14:03:34,096 |  INFO | [TTVH2#TRADE] | ***********:CorresControl:info(357) | Entering CorresControl.getCorresListForLC()  
2025-08-05 14:03:34,097 | DEBUG | [TTVH2#TRADE] | ***********:CorresControl:debug(327) | Parameter passed are txnId = E111020028622257974001, prodCode = null, opnCode = null, toBeDefaulted = Y, hierarchyVals = 'BR0001','110200' 
2025-08-05 14:03:34,097 |  INFO | [TTVH2#TRADE] | ***********:CorresControl:info(357) | Entering CorresControl.getTxnCorres()  
2025-08-05 14:03:34,097 | DEBUG | [TTVH2#TRADE] | ***********:CorresControl:debug(327) |  Parameter passed is txnId E111020028622257974001 
2025-08-05 14:03:34,097 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:34,097 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:34,097 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:34,097 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:34,097 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:34,097 |  INFO | [TTVH2#TRADE] | ***********:CorresControl:info(357) | Database Query :SELECT a.SR_NO, a.ISMANDATORY, a.CORRES_TYPE,  a.PARTY_CODE, a.TRANS_MED_CODE, a.ADDR_SEQ, a.LANG_CODE,  a.TEMPLATE_CODE,  to_char(a.SEND_DATE, 'mm/dd/yyyy') SEND_DATE, a.TRACER_IND, a.TRACER_FREQ , a.TRACER_DAYS,  to_char(a.TRACER_END_DATE, 'mm/dd/yyyy' ) TRACER_END_DATE,  a.FREE_FORM_TEXT , b.SHORT_DESC, b.long_desc, a.ADDRESS, a.CIF_NO, a.CIF_NAME, a.ISSUPPRESSED, a.DECN_PARTY_CODE, a.ISPARTYPREF  from ot_txn_corres a,  ot_corres_type b where a.TXN_ID = ?  AND a.CORRES_TYPE = b.CORRES_TYPE  
2025-08-05 14:03:34,098 |  INFO | [TTVH2#TRADE] | ***********:CorresControl:info(357) | Database Query :select a.party_code party, b.party_name  name, a.ADDRESS address, a.trans_med_code, a.addr_seq  from ot_txn_corres_copy a,  ot_txn_party b   where a.TXN_ID= ? and a.SR_NO = ?  and b.REF_NO = SUBSTR( a.txn_id, 1, 19 )  and b.seq_no = SUBSTR( a.txn_id, 20, 6 )  and a.party_code = b.party_code  
2025-08-05 14:03:34,102 |  INFO | [TTVH2#TRADE] | ***********:CorresControl:info(357) | CorresControl.getTxnCorres : Connection Released weblogic.jdbc.wrapper.PoolConnection_oracle_jdbc_driver_T4CConnection@a6e 
2025-08-05 14:03:34,103 |  INFO | [TTVH2#TRADE] | ***********:CorresControl:info(357) | Leaving CorresControl.getTxnCorres()  
2025-08-05 14:03:34,103 |  INFO | [TTVH2#TRADE] | ***********:CorresSDBControl:info(357) | Entering CorresSDBControl.getCorresDecn()  
2025-08-05 14:03:34,103 | DEBUG | [TTVH2#TRADE] | ***********:CorresSDBControl:debug(327) | Parameter passed are prodType = null, opnType = null, toBeDefaulted = Y, hierarchyVals = 'BR0001','110200' 
2025-08-05 14:03:34,103 |  INFO | [TTVH2#TRADE] | ***********:CorresSDBControl:info(357) | Database Query :select a.HL_NO, a.CORRES_TYPE,   b.SHORT_DESC, b.TRACER_FLAG,  b.HAS_TRACER_FLAG, a.PARTY_TO_SEND, a.ISMANDATORY  from ot_corres_decn a,  ot_corres_type b  where a.corres_type = b.corres_type  and a.PROD_CODE = 'null'  and a.OPN_CODE = 'null' and TO_BE_DEFAULTED='Y' and HL_VAL in ( 'BR0001','110200' ) order by hl_no 
2025-08-05 14:03:34,103 |  INFO | [TTVH2#TRADE] | ***********:CorresSDBControl:info(357) | Database Query :select corres_type, short_desc, TRACER_FREQ,  TRACER_DAYS FROM ot_corres_type  WHERE CORRES_TYPE != ?  START with corres_type = ?  CONNECT BY corres_type =  PRIOR tracer_type 
2025-08-05 14:03:34,103 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:34,103 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:34,103 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:34,103 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:34,103 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:34,115 |  INFO | [TTVH2#TRADE] | ***********:CorresSDBControl:info(357) | CorresSDBControl.getCorresDecn : Connection Released weblogic.jdbc.wrapper.PoolConnection_oracle_jdbc_driver_T4CConnection@a72 
2025-08-05 14:03:34,116 |  INFO | [TTVH2#TRADE] | ***********:CorresSDBControl:info(357) | Leaving CorresSDBControl.getCorresDecn()  
2025-08-05 14:03:34,116 |  INFO | [TTVH2#TRADE] | ***********:CorresControl:info(357) | Leaving CorresControl.getCorresListForLC()  
2025-08-05 14:03:34,116 |  INFO | [TTVH2#TRADE] | ***********:CorresTypeManager:info(357) | Leaving CorresTypeManager.getCorresListForLC()  
2025-08-05 14:03:34,116 | DEBUG | [TTVH2#TRADE] | ***********: BillRetrival:debug(327) | strLCDocCrNo:null 
2025-08-05 14:03:34,116 |  INFO | [TTVH2#TRADE] | ***********:Processing:info(357) |  Entering LCUtil.queryForReferenceNo method 
2025-08-05 14:03:34,116 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:34,117 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:34,117 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:34,117 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:34,117 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:34,117 |  INFO | [TTVH2#TRADE] | ***********:Processing:info(357) | Database Query :select ref_no refNo from ot_lc_txn_details where doc_cr_no = 'null' and (soft_del is null or upper(soft_del) != 'Y') 
2025-08-05 14:03:34,126 |  INFO | [TTVH2#TRADE] | ***********:Processing:info(357) |  Leaving LCUtil.queryForReferenceNo method 
2025-08-05 14:03:34,127 | DEBUG | [TTVH2#TRADE] | ***********: BillRetrival:debug(327) | refNoLC:null 
2025-08-05 14:03:34,127 |  INFO | [TTVH2#TRADE] | ***********:BillEventHandlerEJB:info(357) | BillEventHandler.getBill Connection released weblogic.jdbc.wrapper.PoolConnection_oracle_jdbc_driver_T4CConnection@a49 
2025-08-05 14:03:34,128 | DEBUG | [TTVH2#TRADE] | ***********: RetriveBill:debug(327) | %%%%%%%% cresting pend in cache: Start  
2025-08-05 14:03:34,128 |  INFO | [TTVH2#TRADE] | ***********:PendHelper:info(357) |  Entering PendHelper.getPendData 
2025-08-05 14:03:34,129 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getEJBHome(String, Class) 
2025-08-05 14:03:34,129 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getEJBHome(String, Class) 
2025-08-05 14:03:34,129 |  INFO | [TTVH2#TRADE] | ***********:ProcessingEventHandler:info(357) |  Entering ProcessingEventHandlerEJB.getPendData 
2025-08-05 14:03:34,129 | DEBUG | [TTVH2#TRADE] | ***********:ProcessingEventHandler:debug(327) | refNo null seqNo null actNo  
2025-08-05 14:03:34,130 |  INFO | [TTVH2#TRADE] | ***********:Processing.PendBPH:info(357) |  Entering PendBPH.getPendData 
2025-08-05 14:03:34,130 | DEBUG | [TTVH2#TRADE] | ***********:Processing.PendBPH:debug(327) | refNo null seqNo null actNo  
2025-08-05 14:03:34,130 | DEBUG | [TTVH2#TRADE] | ***********:Processing.PendBPH:debug(327) | before calling pendbph ....nnnnnn 
2025-08-05 14:03:34,130 | DEBUG | [TTVH2#TRADE] | ***********: PendDataHandler:debug(327) |  Inside PendDataHandler.getPendData %%%%*** 
2025-08-05 14:03:34,130 | DEBUG | [TTVH2#TRADE] | ***********: PendDataHandler:debug(327) | Select tx.pend_id, tx.ref_no,tx.seq_no,tx.act_no,tx.pend_date,tx.status,tx.reason_code,tx.to_be_res_by,tx.res_date,tx.res_comments,mst.pend_opn,mst.blocking,mst.description,tx.pend_details from ot_lc_txn_pend tx, ot_pend mst where tx.pend_opn = mst.pend_opn and tx.ref_no ='null' and tx.seq_No='null' 
2025-08-05 14:03:34,131 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:34,131 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:34,131 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:34,131 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:34,131 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:34,132 |  INFO | [TTVH2#TRADE] | ***********: PendDataHandler:info(357) | PendDataHandler.getPendData : Connection Taken weblogic.jdbc.wrapper.JTSConnection_oracle_jdbc_driver_T4CConnection@a79 
2025-08-05 14:03:34,143 |  INFO | [TTVH2#TRADE] | ***********: PendDataHandler:info(357) | PendDataHandler.getPendData : Connection Released weblogic.jdbc.wrapper.JTSConnection_oracle_jdbc_driver_T4CConnection@a79 
2025-08-05 14:03:34,143 |  INFO | [TTVH2#TRADE] | ***********:Processing.PendBPH:info(357) |  Leaving PendBPH.getPendData 
2025-08-05 14:03:34,143 |  INFO | [TTVH2#TRADE] | ***********:ProcessingEventHandler:info(357) |  Leaving ProcessingEventHandlerEJB.getPendData 
2025-08-05 14:03:34,146 |  INFO | [TTVH2#TRADE] | ***********:PendHelper:info(357) |  Leaving PendHelper.getPendData 
2025-08-05 14:03:34,147 | DEBUG | [TTVH2#TRADE] | ***********: RetriveBill:debug(327) | pendList in invocation of pend is = [] 
2025-08-05 14:03:34,147 |  INFO | [TTVH2#TRADE] | ***********:Processing:info(357) |  Inside PendCacheAccessor.putPendintoCache 
2025-08-05 14:03:34,147 | DEBUG | [TTVH2#TRADE] | ***********:Processing:debug(327) | pendList before putting into cache in PendCacheAccessor = [] 
2025-08-05 14:03:34,147 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | inside CacheManager.putCache method. 
2025-08-05 14:03:34,148 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | Putting objIdentifier.nullnull 
2025-08-05 14:03:34,148 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | Putting mapID.com.orbitech.orbitrade.util.cache.CachedObject@6c1a4391 
2025-08-05 14:03:34,148 | DEBUG | [TTVH2#TRADE] | ***********: RetriveBill:debug(327) | %%%%%%%% cresting pend in cache: END  
2025-08-05 14:03:34,148 | DEBUG | [TTVH2#TRADE] | ***********: CommonInfoOrbicore:debug(327) | Entering getMailAddressForCif with cif:null 
2025-08-05 14:03:34,149 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:34,149 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:34,149 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:34,149 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:34,149 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:34,149 |  INFO | [TTVH2#TRADE] | ***********: CommonInfoOrbicore:info(357) | Database Query : Select CIF_NO, BR_CD,CO_NAME, F_NAME, M_NAME, L_NAME, LANG_PREF,BUSINESS_CODE,OFF_ADD_1, OFF_ADD_2,OFF_ADD_3,OFF_ADD_4,OFF_CITY,OFF_CNTRY_CD ,OFF_PIN_CD,LEG_ADD_1,LEG_ADD_2,LEG_ADD_3,  LEG_ADD_4, LEG_CITY,LEG_CNTRY_CD,LEG_PIN_CD,RES_ADD_1,RES_ADD_2,RES_ADD_3,RES_ADD_4, RES_CITY, RES_CNTRY_CD,RES_PIN_CD,EMAIL_ID1,EMAIL_ID2,FAX_NO, OFF_PH_NO_1, OFF_PH_NO_2, RES_PH_NO_1, RES_PH_NO_2, LEG_PH_NO_1, LEG_PH_NO_2, LL_NAME,CITIZEN_NO from GR010MB where CIF_NO = ? and Status !='C'  order by CO_NAME,F_NAME||M_NAME||L_NAME, OFF_CNTRY_CD, OFF_CITY 
2025-08-05 14:03:34,151 | DEBUG | [TTVH2#TRADE] | ***********: CommonInfoOrbicore:debug(327) | Office address :Res Addr: 
2025-08-05 14:03:34,151 | DEBUG | [TTVH2#TRADE] | ***********: CommonInfoOrbicore:debug(327) | Entering getCountrynName 
2025-08-05 14:03:34,152 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:34,152 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:34,152 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:34,152 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:34,152 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:34,154 | DEBUG | [TTVH2#TRADE] | ***********: CommonInfoOrbicore:debug(327) | Country name for:  is : 
2025-08-05 14:03:34,154 | DEBUG | [TTVH2#TRADE] | ***********: CommonInfoOrbicore:debug(327) | Leaving getCountrynName: 
2025-08-05 14:03:34,155 | DEBUG | [TTVH2#TRADE] | ***********: CommonInfoOrbicore:debug(327) | Entering getCountrynName 
2025-08-05 14:03:34,155 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Entering : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:34,155 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | InitialContext javax.naming.InitialContext@3f1f3bb5 
2025-08-05 14:03:34,155 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | dataSource weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:34,155 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | Leaving : ServiceLocator.getDataSource(String) 
2025-08-05 14:03:34,156 | DEBUG | [TTVH2#TRADE] | ***********:ServiceLocator:debug(327) | DataSourceName : weblogic.jdbc.common.internal.RmiDataSource@3bec4e62 
2025-08-05 14:03:34,157 | DEBUG | [TTVH2#TRADE] | ***********: CommonInfoOrbicore:debug(327) | Country name for:  is : 
2025-08-05 14:03:34,157 | DEBUG | [TTVH2#TRADE] | ***********: CommonInfoOrbicore:debug(327) | Leaving getCountrynName: 
2025-08-05 14:03:34,158 | DEBUG | [TTVH2#TRADE] | ***********: CommonInfoOrbicore:debug(327) | leaving getMailAddressForCif with address :[, ] 
2025-08-05 14:03:34,159 |  INFO | [TTVH2#TRADE] | ***********: BranchListAccessor:info(357) |  Inside BranchListAccessor.getProduct 
2025-08-05 14:03:34,159 |  INFO | [TTVH2#TRADE] | ***********: BranchListAccessor:info(357) |  Inside BranchListAccessor.getBranch 
2025-08-05 14:03:34,159 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | inside CacheManager.getCache method. 
2025-08-05 14:03:34,159 | DEBUG | [TTVH2#TRADE] | ***********:CacheManager:debug(327) | strMapID is BRANCHLISTMAP 
2025-08-05 14:03:34,159 |  INFO | [TTVH2#TRADE] | ***********: BranchListAccessor:info(357) |  Leaving BranchListAccessor.getBranch 
