Inside Get Session in Nsec Session Initaiator
Inside Get Session in Nsec Session Initaiator
Inside Get Session in Nsec Session Initaiator
Inside Get Session in Nsec Session Initaiator
Inside Get Session in Nsec Session Initaiator
strDateFormat ==>MMM dd, yyyy
isStandard ==>false
AmedDetails Query select SEQ_NO from OT_BILL_TXN_DETAILS where BILL_NO ='TLO130921IBLU459' and OPN_CODE='BLAMDS'
refNum = E111060025621145057
seqNum = 003
partyCode = APP
partyCategory = C
refNum = E111060025621145057
seqNum = 003
partyCode = BEN
partyCategory = O
refNum = E111060025621145057
seqNum = 003
partyCode = IMB
partyCategory = B
refNum = E111060025621145057
seqNum = 003
partyCode = ISB
partyCategory = B
refNum = E111060025621145057
seqNum = 003
partyCode = PRB
partyCategory = B
Issue Dt:             Expiry Dt:
*** arraAccntinfoObj.size=0
*** arraAccntinfoObj.size=0
*** arraAccntinfoObj.size=0
*** arraAccntinfoObj.size=0
Roshan Before check for 77J:: + C/O:     ++ NOT ISSUED IN FORM E    ++ 01 ORIGINAL AND 05 COPIES PRESENTED I/O 03 ORIGINALS AND 03 COPIES AS L/C TERMS.
Roshan After check for 77J:: + C/O:     ++ NOT ISSUED IN FORM E    ++ 01 ORIGINAL AND 05 COPIES PRESENTED I/O 03 ORIGINALS AND 03 COPIES AS L/C TERMS.
Roshan Before check for 77J:: + BANK LETTER AS PER FIELD 46A, ITEM 3 NOT PRESENTED
Roshan After check for 77J:: + BANK LETTER AS PER FIELD 46A, ITEM 3 NOT PRESENTED
Roshan Before check for 77J:: + ALL DOCUMENTS (EXCEPT DRAFTS): SHOWING COMMODITY ITEM 3 (BUCKETS) CONFLICTS WITH L/C(SOIL BUCKET DIAMETER 1.2M AND 1.5M I/O SOIL  BUCKET DIAMETER 2M)
Roshan After check for 77J:: + ALL DOCUMENTS (EXCEPT DRAFTS): SHOWING COMMODITY ITEM 3 (BUCKETS) CONFLICTS WITH L/C(SOIL BUCKET DIAMETER 1.2M AND 1.5M I/O SOIL  BUCKET DIAMETER 2M)
Prior to getting data source name
The datasource nameSITPool
 st.countTokens() 2
 strLCValue  *Issue Dt:             Expiry Dt:*
 strLCValue  **
 strLCValue  **
 strLCValue  **
 iRows util 15
 iColumns util 31
 the text area <TEXTAREA  NAME="MISC5" ROWS="15" COLS="31"  ></TEXTAREA>
 strLCValue  **
 iRows util 20
 iColumns util 15
 the text area <TEXTAREA  NAME="MISC6" ROWS="20" COLS="15" onBlur="checkSwiftCharSet(this);" ></TEXTAREA>
